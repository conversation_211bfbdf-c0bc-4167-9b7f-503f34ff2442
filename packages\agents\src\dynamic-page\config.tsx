import { AgentConfig, Role } from "@cscs-agent/core";
import { Copy, PromptTemplate, Rating } from "@cscs-agent/presets";

import { prompts } from "./prompts";
import { defaultSuggestions } from "./suggestions";
import PreviewButton from "./widgets/preview-button/PreviewButton";
import Preview from "./widgets/preview/Preview";

export const agentConfig: AgentConfig = {
  name: "动态页面智能体",
  code: "dynamic-page-creator",
  logo: "/assets/dynamic-page-creator-logo.png",
  welcome: "Hi，欢迎使用动态页面生成助手",
  description: "可通过 SQL语言的查询语句，智能生成页面；也可指定页面名称或编码，智能编辑页面。",
  message: {
    blocks: {
      widgets: [
        {
          code: "@DynamicPage/PreviewButton",
          component: PreviewButton,
        },
      ],
    },
    slots: {
      footer: {
        widgets: [
          {
            code: "Copy",
            component: Copy,
            role: Role.AI,
          },
          {
            code: "Rating",
            component: Rating,
            role: Role.AI,
          },
        ],
      },
    },
  },
  prompts,
  commands: [
    {
      name: "",
      action: () => {},
    },
  ],
  suggestions: defaultSuggestions,
  sender: {
    slots: {
      headerPanel: {
        widgets: [
          {
            code: "PromptTemplate",
            component: PromptTemplate,
          },
        ],
      },
      footer: {
        widgets: [],
      },
    },
  },
  sidePanel: {
    render: {
      widgets: [
        {
          code: "@DynamicPage/SidePanelPreview",
          component: Preview,
          props: {
            saveApiUrl: "/gateway/cluster/page/system/dynamic/page/manage/savePageForAi",
            previewUrl: (id: string) => `/dynamic-page/temporary-preview?id=${id}`,
          },
        },
      ],
    },
  },
  request: {
    chat: {
      url: "/chat/completion",
      headers: {},
      method: "get",
    },
  },
};

export function dynamicPageConfigFactory(params: { saveApiUrl: string; previewUrl: string }) {
  const widget = agentConfig.sidePanel?.render.widgets?.find((i) => i.code === "@DynamicPage/SidePanelPreview");
  if (widget) {
    widget.props = {
      ...widget.props,
      ...params,
    };
  }
  return agentConfig;
}
