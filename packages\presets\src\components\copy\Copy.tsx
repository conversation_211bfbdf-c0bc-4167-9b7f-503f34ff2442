import { Button, Tooltip, message as antdMessage } from "antd";
import React from "react";

import { CopyOutlined } from "@ant-design/icons";
import { MessageContext } from "@cscs-agent/core";

const Copy: React.FC = () => {
  const context = React.useContext(MessageContext);

  const handleCopy = () => {
    const message = context?.message.getTextContent();
    if (!message) return;
    navigator.clipboard.writeText(message);
    antdMessage.success("复制成功");
  };

  return (
    <Button
      type="text"
      size="small"
      icon={
        <span className="presets:text-color-65">
          <Tooltip title="复制">
            <CopyOutlined onClick={handleCopy} />
          </Tooltip>
        </span>
      }
    ></Button>
  );
};

export default Copy;
