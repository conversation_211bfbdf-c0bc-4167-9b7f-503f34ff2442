import { Alert } from "antd";
import React from "react";
import Markdown from "react-markdown";
import rehypeHighlight from "rehype-highlight";
import rehypeRaw from "rehype-raw";
import remarkGfm from "remark-gfm";

import { BlockStatus } from "@/types";

interface ErrorBlockProps {
  content: string;
  status: BlockStatus;
}

const ErrorBlock: React.FC<ErrorBlockProps> = (props) => {
  const { content } = props;

  return (
    <div className="ag:mb-2">
      <Alert
        message="Error"
        description={
          <div className="ag:break-all ag:leading-[1.6em]">
            <Markdown rehypePlugins={[rehypeHighlight, rehypeRaw]} remarkPlugins={[remarkGfm]}>
              {content}
            </Markdown>
          </div>
        }
        type="error"
        showIcon
        className="ag:mb-2"
      />
    </div>
  );
};

export default ErrorBlock;
