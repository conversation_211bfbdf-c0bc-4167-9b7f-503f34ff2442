import { nanoid } from "nanoid";
import React, { PropsWithChildren, useMemo } from "react";

import { AgentChatConfig } from "@/types";

import { AgentChatContext } from "../state/context";
import { AgentStore } from "../state/store";
import AgentCore from "./AgentCore";

export interface AgentChatHostProps {
  config: AgentChatConfig;
}

const AgentChatHost: React.FC<PropsWithChildren<AgentChatHostProps>> = (props) => {
  const { config, children } = props;

  const contextValue = useMemo(
    () => ({
      agentId: nanoid(),
      store: new AgentStore(),
      config,
    }),
    [],
  );

  return (
    <AgentChatContext.Provider value={contextValue}>
      <AgentCore>{children}</AgentCore>
    </AgentChatContext.Provider>
  );
};

export default AgentChatHost;
