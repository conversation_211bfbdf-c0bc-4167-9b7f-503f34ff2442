import { Button, Space, Tooltip, message as antdMessage } from "antd";
import React from "react";

import { DislikeFilled, DislikeOutlined, LikeFilled, LikeOutlined } from "@ant-design/icons";
import { MessageContext, MessageStatus, post } from "@cscs-agent/core";

const Rating: React.FC = () => {
  const context = React.useContext(MessageContext);
  const [rating, setRating] = React.useState<"like" | "dislike" | null>(context?.message.user_rating ?? null);

  const handleRating = ($rating: "like" | "dislike") => {
    if (!context?.message) return;

    // message loading 状态不允许操作
    if (context?.message.status === MessageStatus.Loading) {
      antdMessage.warning("请等待消息加载完成");
      return;
    }

    const message = context.message;
    post(`/message/${message.id}/rate`, {
      rating: $rating,
    }).then(() => {
      setRating($rating);
    });
  };

  return (
    <Space>
      <Tooltip title="喜欢">
        <Button
          type="text"
          size="small"
          icon={<span className="presets:text-color-65">{rating === "like" ? <LikeFilled /> : <LikeOutlined />}</span>}
          onClick={() => handleRating("like")}
        ></Button>
      </Tooltip>
      <Tooltip title="不喜欢">
        <Button
          type="text"
          size="small"
          icon={
            <span className="presets:text-color-65">{rating === "dislike" ? <DislikeFilled /> : <DislikeOutlined />}</span>
          }
          onClick={() => handleRating("dislike")}
        ></Button>
      </Tooltip>
    </Space>
  );
};

export default Rating;
