import { <PERSON><PERSON>, <PERSON>, Row } from "antd";
import React, { useEffect } from "react";

import { SyncOutlined } from "@ant-design/icons";
import { BuildInCommand, StandardResponse, get, useActiveAgentCode, useCommandRunner } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

interface SuggestionConfig {
  prompt: string;
  heat: number;
}

const PopularQuestion: React.FC = () => {
  const [activeAgentCode] = useActiveAgentCode();
  const [questions, setQuestions] = React.useState<SuggestionConfig[]>([]);
  const runner = useCommandRunner();

  useEffect(() => {
    setQuestions([]);
    if (!activeAgentCode) return;
    get<StandardResponse<SuggestionConfig[]>>(`/popular-question`, { agent_code: activeAgentCode }).then((res) => {
      setQuestions(res.data.data);
    });
  }, [activeAgentCode]);

  const handleClick = (question: string) => {
    runner(BuildInCommand.SendMessage, { message: question, isNewConversation: true, agentCode: activeAgentCode });
  };

  return (
    <div className="presets:w-[800px]">
      <div className="presets:flex presets:mb-3 presets:justify-between">
        <div>
          <Icon icon="Chat" className="presets:text-[#6C90F2]" />
          <span className="presets:text-sm presets:text-dark presets:ml-2">猜你想问</span>
        </div>
        <Button
          type="text"
          size="small"
          icon={
            <span className="presets:text-color-45">
              <SyncOutlined />
            </span>
          }
        >
          <span className="presets:text-color-45">换一换</span>
        </Button>
      </div>
      <Row gutter={[16, 16]}>
        {questions.map((question) => (
          <Col span={12} key={question.prompt}>
            <div
              onClick={() => handleClick(question.prompt)}
              className="presets:bg-white presets:rounded-sm presets:px-4 presets:py-2 presets:border presets:border-[rgba(74,88,172,0.09)] presets:text-color-65 presets:hover:cursor-pointer"
            >
              {question.prompt}
            </div>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default PopularQuestion;
