export enum MessageErrorCode {
  IncompletePackage = "INCOMPLETE_PACKAGE",
  InvalidChunk = "INVALID_CHUNK",
  ParsingError = "PARSING_ERROR",
  NetworkError = "NETWORK_ERROR",
  ServerError = "SERVER_ERROR",
}

export class MessageError {
  message: string;
  code: string;
  error: any;

  constructor(message: string, code: MessageErrorCode, error: any) {
    this.message = message;
    this.code = code;
    this.error = error;
  }
}
